/*
 * @Description:量表
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';


/**
 * @description: 测评提交
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const addSnapEvaInfo = (data) => {
	return httpRequest.post('SnapEvaluation/1.0/addSnapEvaInfo', data);
};

/**
 * @description: 测评结果
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const getQrySnapEvaDetail = (data) => {
	return httpRequest.post('SnapEvaluation/1.0/qrySnapEvaDetail', data);
};

/**
 * @description: 获取问卷题目列表
 * @author: 许江涛
 * @return {*}
 * @param {*} data { projectId: string }
 */
export const getQuestionItemList = (data) => {
	return httpRequest.post('question/v1.1/getQuestionItemList', data);
};

/**
 * @description: 导出测评结果
 * @author: 小雨
 * @return {*}
 * @param {*} authCode
 */
export const exportSnapEvaDetail = (data) => {
	return httpRequest.post('SnapEvaluation/1.0/exportSnapEvaDetail', data);
};