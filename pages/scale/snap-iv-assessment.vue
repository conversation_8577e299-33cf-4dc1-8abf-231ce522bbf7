<template>
	<view class="question">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<image src="/static/nav-back-icon.png" class="back-icon"></image>
			</view>
			<text class="title">SNAP-IV父母评定问卷</text>
		</view>
		
		<view class="question-schedule">
			<progress class="question-schedule-icon" :percent="progressPercent" :show-info="false" border-radius="50" activeColor="#41A8DE" stroke-width="8" />
			<view class="question-schedule-text"><text class="question-schedule-text-num">{{currentQuestionIndex}}</text>/{{totalQuestions}}</view>
		</view>
		<view class="question-content">
			<view class="question-content__text">请根据您孩子过去一周内的实际情况选择</view>
			<view class="question-content__topic">
				<text class="question-content__topic-text">{{currentQuestion.text}}</text>
				<view v-for="option in options" class="question-content__topic-options" :class="[selectedAnswer==option.value?'question-content__topic-options-clicked':'']" :key="option.value"
					@click="selectOption(option)">
					{{option.text}}
				</view>
			</view>
		</view>
		<view class="question-bottom">
			<view class="question-bottom-next center" @click="goToPrevious" v-if="showPreviousButton">
				<text class="question-bottom-next-text">上一题</text>
			</view>
		</view>
		<uni-popup ref="finishPopup" type="center" :animation="false">
			<view class="popup-box" v-if="questionConfig.isFinish">
				<view class="popup-box-top">
					<view class="popup-box-top__text">
						<view class="popup-box-top__text-top">确认提交问卷</view>
					</view>
				</view>
				<view class="popup-box-center">本次问卷已完成，是否提交本次评估结果<view class="">
					</view>
				</view>
				<view class="popup-box__button">
					<view class="popup-box__button-left" @click="cancelSubmit">取消</view>
					<view class="popup-box__button-right" @click="confirmSubmit">确认</view>
				</view>
			</view>
			<view class="popup-box" v-else style="width: 960rpx;">
				<view class="popup-box-top">
					<view class="popup-box-top__text">
						<view class="popup-box-top__text-top">指导语</view>
					</view>
				</view>
				<view class="popup-box-center">
					0分一从来没有；<br />
					1分一有时(偶尔每月几次)；<br />
					2分一经常 (每周几次)；<br />
					3分一非常常见(每天一次或者多次，或者每周几天一天多次)。<br />
					如果评分人在1分与2分之间难以选择时，请选择1分。
				</view>
				<view class="popup-box__button">
					<view class="popup-box__button-only" @click="closeGuide">我已知晓</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		computed,
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		navigateTo,
		redirectTo,
		getStorageSync,
		showToast
	} from "../../common/uniTool";
	import {
		addSnapEvaInfo
	} from "../../service/scale";
	import {
		onShow,
	} from '@dcloudio/uni-app'
	import {
		useUserStore
	} from '../../stores/user';
	
	const userStore = useUserStore()
	const finishPopup = ref(null)
	
	const questionConfig = reactive({
		answers: [],
		questionIndex: 1,
		answer: -1,
		isClick: true,
		isEnd: -1,
		isFinish: false
	})
	
	const options = ref([{
		value: 0,
		text: '没有'
	}, {
		value: 1,
		text: '有一点'
	}, {
		value: 2,
		text: '不少'
	}, {
		value: 3,
		text: '很多'
	}])
	
	const questions = ref([
		'学习、做事时不注意细节，出现粗心大意的错误',
		'很难维持注意力集中（听课，作业，谈话，或长时间阅读）',
		'与他/她讲话或口头指令时似听非听',
		'不能按要求完成作业及家务（开始任务后容易分心，很快失去注意力）',
		'很难有条理的安排任务和活动（任务无序、做事凌乱，没时间概念）',
		'不愿或回避需要持续动脑的任务（课堂作业或家庭作业）',
		'丢失学习和活动的必需品（学习资料、文具、红领巾、衣物）',
		'因干扰而分心',
		'日常生活中健忘、多动、冲动',
		'坐立不安、手脚不停',
		'在需要坐着的场合离开座位（教室里和其他公共场所）',
		'在不适宜的场所里跑来跑去、爬上爬下',
		'很难安静的参加同伴游戏或课余活动',
		'一刻不停，显得"精力旺盛"',
		'话很多',
		'问题没问完就抢着回答',
		'很难耐心排队或等待',
		'打断或干扰别人（如插话）对立违抗',
		'与大人争执',
		'发脾气',
		'不服从或拒绝遵从家长的要求或规定',
		'故意打扰别人',
		'把自己的错误和行为归咎于别人',
		'容易发怒或被激怒',
		'愤怒或怀恨在心',
		'充满恨意，想报复'
	])
	
	// 计算属性
	const currentQuestionIndex = computed(() => questionConfig.questionIndex)
	const selectedAnswer = computed(() => questionConfig.answer)
	const totalQuestions = computed(() => questions.value.length)
	const progressPercent = computed(() => (questionConfig.questionIndex / questions.value.length) * 100)
	const currentQuestion = computed(() => ({ text: questions.value[questionConfig.questionIndex - 1] || '' }))
	const showPreviousButton = computed(() => questionConfig.questionIndex > 1)
	
	onMounted(() => {
		finishPopup.value.open('center')
	})
	
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif
	})

	const closeGuide = () => {
		finishPopup.value.close()
		questionConfig.answers.pop()
	}
	
	const confirmSubmit = () => {
		addSnapEvaInfo({
			signalData: questionConfig.answers,
			outpatientId: userStore.outpatientId,
			userName: userStore.userName,
		}).then(res => {
			redirectTo('/pages/report/index')
		}).catch(err => {
			showToast(err.msg)
		})
	}
	
	const selectOption = (option) => {
		if (!questionConfig.isClick) {
			return
		}
		questionConfig.answer = option.value
		questionConfig.isClick = false
		setTimeout(() => {
			if (questionConfig.answers.length == questions.value.length) {
				questionConfig.answers[17] = `17,${option.value}`
			} else {
				questionConfig.answers.push(`${questionConfig.questionIndex},${option.value}`)
			}

			if (questionConfig.questionIndex >= questions.value.length) {
				questionConfig.isFinish = true
				finishPopup.value.open('center')
				questionConfig.answer = option.value
				questionConfig.isClick = true
				return
			}
			questionConfig.questionIndex += 1
			questionConfig.answer = -1
			questionConfig.isClick = true
		}, 250)
	}
	
	const goToPrevious = () => {
		questionConfig.questionIndex -= 1
		questionConfig.answer = questionConfig.answers[questionConfig.questionIndex - 1].split(',')[1]
		questionConfig.answers.pop()
	}
	
	const cancelSubmit = () => {
		finishPopup.value.close()
		questionConfig.answers.pop()
	}
	
	const goBack = () => {
		uni.navigateBack()
	}
</script>

<style lang="scss">
	.popup-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		width: 740rpx;
		background: #ffffff;
		border-radius: 20rpx;
		padding: 0 40rpx;

		&-top {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;

			&__icon {
				width: 160rpx;
				height: 160rpx;
			}

			&__text {
				display: flex;
				flex-direction: column;
				align-items: center;

				&-top {
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #111111;
					margin-bottom: 80rpx;
					margin-top: 80rpx;
				}
			}
		}

		&-center {
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 48rpx;
			margin-bottom: 72rpx;
		}

		&__button {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 40rpx;

			&-left {
				width: 260rpx;
				height: 120rpx;
				background: rgba($color: #41A8DE, $alpha: 0.1);
				border-radius: 24rpx;
				font-size: 40rpx;
				font-weight: 600;
				color: #41A8DE;
				text-align: center;
				line-height: 120rpx;
			}

			&-right {
				width: 260rpx;
				height: 120rpx;
				background: #41A8DE;
				border-radius: 24rpx;
				font-size: 40rpx;
				font-weight: 600;
				color: #ffffff;
				text-align: center;
				line-height: 120rpx;
			}

			&-only {
				width: 560rpx;
				height: 120rpx;
				background: #41A8DE;
				border-radius: 24rpx;
				font-size: 40rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #ffffff;
				text-align: center;
				line-height: 120rpx;
				margin: 0 auto;
			}
		}
	}

	.question {
		width: 100vw;
		height: 100vh;
		background: #f6f6f6;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		position: relative;

		/* 顶部导航栏 */
		.header {
			position: relative;
			width: 100%;
			height: 82px;
			background: #FFFFFF;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-btn {
				position: absolute;
				left: 37px;
				width: 30px;
				height: 30px;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.back-icon {
					width: 24px;
					height: 24px;
				}
			}
			
			.title {
				font-family: 'Alibaba PuHuiTi', 'PingFang SC';
				font-size: 30px;
				color: #333333;
				line-height: 30px;
				font-weight: 600;
			}
		}

		&-schedule {
			display: flex;
			width: 100%;
			flex-direction: column;
			align-items: center;

			&-icon {
				width: 100%;
			}

			&-text {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				margin-top: 40rpx;

				&-num {
					font-size: 48rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
				}
			}
		}

		&-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
			flex: 1;

			&__text {
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				margin-bottom: 40rpx;
			}

			&__topic {
				width: 88%;
				background: #ffffff;
				border-radius: 32rpx;
				padding: 40rpx 40rpx 40rpx 40rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
					line-height: 56rpx;
					height: 128rpx;
					display: flex;
					align-items: center;
				}

				&-options {
					width: 590rpx;
					height: 120rpx;
					margin-bottom: 20rpx;
					background: #f6f6f6;
					border-radius: 60rpx;
					text-align: center;
					line-height: 120rpx;
					color: #111111;
					font-size: 36rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;


					&-clicked {
						background: #41A8DE;
						color: #FFFFFF;
					}
				}
			}
		}

		&-bottom {
			width: 670rpx;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 40rpx;

			&-next {
				width: 510rpx;
				height: 100rpx;
				background: #f6f6f6;
				border-radius: 60rpx;
				border: 2rpx solid #41A8DE;
				margin: 0 auto;

				&-text {
					font-size: 36rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #41A8DE;
				}
			}

			&-one {
				width: 100%;
				height: 100%;
				background: #41A8DE;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			&-left {
				width: 315rpx;
				height: 120rpx;
				background: #e5f0e6;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #41A8DE;
				}
			}

			&-right {
				width: 315rpx;
				height: 120rpx;
				background: #41A8DE;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}
	}
</style>
